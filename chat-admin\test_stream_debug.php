<!DOCTYPE html>
<html>
<head>
    <title>Тест стриминга</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        #log { height: 300px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; background: #f9f9f9; }
    </style>
</head>
<body>
    <h1>🔧 Тест стриминга</h1>
    
    <div class="test-section">
        <h2>1. Тест prepareStream</h2>
        <button onclick="testPrepareStream()">Тест prepareStream</button>
        <div id="prepare-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Тест streamProxy</h2>
        <button onclick="testStreamProxy()">Тест streamProxy</button>
        <div id="stream-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Лог событий</h2>
        <button onclick="clearLog()">Очистить лог</button>
        <div id="log"></div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${time}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        async function testPrepareStream() {
            const resultDiv = document.getElementById('prepare-result');
            resultDiv.innerHTML = '<div class="info">Тестируем prepareStream...</div>';
            
            try {
                log('Отправляем запрос prepareStream...');
                
                const response = await fetch('index.php?page=api&action=prepareStream', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        session_id: 1,
                        user_message: 'Тестовое сообщение'
                    })
                });
                
                log(`Ответ prepareStream: ${response.status} ${response.statusText}`);
                
                const result = await response.json();
                log(`Данные prepareStream: ${JSON.stringify(result)}`);
                
                if (response.ok && result.status === 'success') {
                    resultDiv.innerHTML = `
                        <div class="success">✅ prepareStream работает!</div>
                        <div class="info">Request ID: ${result.request_id}</div>
                    `;
                    
                    // Сохраняем request_id для следующего теста
                    window.testRequestId = result.request_id;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ prepareStream ошибка</div>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                log(`Ошибка prepareStream: ${error.message}`);
                resultDiv.innerHTML = `<div class="error">❌ Ошибка: ${error.message}</div>`;
            }
        }
        
        async function testStreamProxy() {
            const resultDiv = document.getElementById('stream-result');
            
            if (!window.testRequestId) {
                resultDiv.innerHTML = '<div class="error">❌ Сначала запустите prepareStream</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="info">Тестируем streamProxy...</div>';
            
            try {
                log(`Подключаемся к streamProxy с request_id: ${window.testRequestId}`);
                
                const url = `index.php?page=api&action=streamProxy&request_id=${window.testRequestId}`;
                log(`URL: ${url}`);
                
                const eventSource = new EventSource(url);
                
                let messageCount = 0;
                let hasError = false;
                
                eventSource.onopen = function(event) {
                    log('✅ EventSource подключен');
                    resultDiv.innerHTML = '<div class="success">✅ Подключение установлено, ожидаем данные...</div>';
                };
                
                eventSource.onmessage = function(event) {
                    messageCount++;
                    log(`📨 Получено сообщение #${messageCount}: ${event.data}`);
                    
                    try {
                        const data = JSON.parse(event.data);
                        if (data.type === 'text_chunk') {
                            log(`📝 Текстовый чанк: "${data.data}"`);
                        } else if (data.type === 'end') {
                            log('🏁 Поток завершен');
                            eventSource.close();
                            resultDiv.innerHTML = `
                                <div class="success">✅ streamProxy работает!</div>
                                <div class="info">Получено сообщений: ${messageCount}</div>
                            `;
                        } else if (data.type === 'error') {
                            log(`❌ Ошибка в потоке: ${data.message}`);
                            hasError = true;
                        }
                    } catch (e) {
                        log(`⚠️ Не удалось распарсить JSON: ${event.data}`);
                    }
                };
                
                eventSource.onerror = function(event) {
                    log(`❌ Ошибка EventSource: ReadyState=${eventSource.readyState}`);
                    hasError = true;
                    
                    if (eventSource.readyState === EventSource.CLOSED) {
                        log('🔌 Соединение закрыто');
                        if (!hasError && messageCount === 0) {
                            resultDiv.innerHTML = '<div class="error">❌ Соединение закрыто без получения данных</div>';
                        }
                    }
                };
                
                // Таймаут для теста
                setTimeout(() => {
                    if (eventSource.readyState !== EventSource.CLOSED) {
                        log('⏰ Таймаут теста, закрываем соединение');
                        eventSource.close();
                        if (messageCount === 0) {
                            resultDiv.innerHTML = '<div class="error">❌ Таймаут: данные не получены</div>';
                        }
                    }
                }, 10000);
                
            } catch (error) {
                log(`Ошибка streamProxy: ${error.message}`);
                resultDiv.innerHTML = `<div class="error">❌ Ошибка: ${error.message}</div>`;
            }
        }
        
        // Автоматически запускаем prepareStream
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(testPrepareStream, 500);
        });
    </script>
</body>
</html>
