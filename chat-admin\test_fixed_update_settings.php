<!DOCTYPE html>
<html>
<head>
    <title>Тест исправленного updateSettings</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔧 Тест исправленного updateSettings</h1>
    
    <div class="test-section">
        <h2>1. Тест с валидными настройками</h2>
        <button onclick="testValidSettings()">Тест валидных настроек</button>
        <div id="valid-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Тест с невалидными настройками</h2>
        <button onclick="testInvalidSettings()">Тест невалидных настроек</button>
        <div id="invalid-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Тест смешанных настроек</h2>
        <button onclick="testMixedSettings()">Тест смешанных настроек</button>
        <div id="mixed-result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. Полный тест API</h2>
        <button onclick="testFullAPI()">Полный тест</button>
        <div id="full-result"></div>
    </div>

    <script>
        async function testValidSettings() {
            const resultDiv = document.getElementById('valid-result');
            resultDiv.innerHTML = '<div class="info">Тестируем валидные настройки...</div>';
            
            try {
                const validData = {
                    notification_email: '<EMAIL>',
                    smtp_enabled: 1,
                    smtp_host: 'smtp.test.com',
                    smtp_port: 587,
                    preview_messages_enabled: 1
                };
                
                const response = await fetch('index.php?page=api&action=updateSettings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(validData)
                });
                
                const result = await response.json();
                
                if (response.ok && result.status === 'success') {
                    resultDiv.innerHTML = `
                        <div class="success">✅ Валидные настройки работают!</div>
                        <div class="info">${result.message}</div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ Ошибка с валидными настройками</div>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Ошибка: ${error.message}</div>`;
            }
        }
        
        async function testInvalidSettings() {
            const resultDiv = document.getElementById('invalid-result');
            resultDiv.innerHTML = '<div class="info">Тестируем невалидные настройки...</div>';
            
            try {
                const invalidData = {
                    non_existent_column: 'test_value',
                    another_fake_column: 123,
                    test: 'should_fail'
                };
                
                const response = await fetch('index.php?page=api&action=updateSettings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(invalidData)
                });
                
                const result = await response.json();
                
                if (result.status === 'error') {
                    resultDiv.innerHTML = `
                        <div class="success">✅ Невалидные настройки корректно отклонены!</div>
                        <div class="info">Сообщение: ${result.message}</div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="warning">⚠️ Невалидные настройки не были отклонены</div>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Ошибка: ${error.message}</div>`;
            }
        }
        
        async function testMixedSettings() {
            const resultDiv = document.getElementById('mixed-result');
            resultDiv.innerHTML = '<div class="info">Тестируем смешанные настройки...</div>';
            
            try {
                const mixedData = {
                    notification_email: '<EMAIL>', // валидная
                    smtp_enabled: 1, // валидная
                    fake_setting: 'should_fail', // невалидная
                    smtp_port: 465 // валидная
                };
                
                const response = await fetch('index.php?page=api&action=updateSettings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(mixedData)
                });
                
                const result = await response.json();
                
                if (result.status === 'error' && result.message.includes('fake_setting')) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ Смешанные настройки корректно обработаны!</div>
                        <div class="info">Невалидные настройки отклонены: ${result.message}</div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="warning">⚠️ Неожиданный результат</div>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Ошибка: ${error.message}</div>`;
            }
        }
        
        async function testFullAPI() {
            const resultDiv = document.getElementById('full-result');
            resultDiv.innerHTML = '<div class="info">Полный тест API...</div>';
            
            try {
                let html = '<h3>🧪 Полный тест API</h3>';
                
                // 1. Тест getSettings
                html += '<h4>1. getSettings</h4>';
                const getResponse = await fetch('index.php?page=api&action=getSettings');
                const getResult = await getResponse.json();
                
                if (getResponse.ok && getResult.status === 'success') {
                    html += '<div class="success">✅ getSettings работает</div>';
                } else {
                    html += `<div class="error">❌ getSettings: ${getResult.message || 'Ошибка'}</div>`;
                }
                
                // 2. Тест getTexts
                html += '<h4>2. getTexts</h4>';
                const textsResponse = await fetch('index.php?page=api&action=getTexts');
                const textsResult = await textsResponse.json();
                
                if (textsResponse.ok && textsResult.status === 'success') {
                    html += `<div class="success">✅ getTexts работает (${textsResult.texts.length} текстов)</div>`;
                } else {
                    html += `<div class="error">❌ getTexts: ${textsResult.message || 'Ошибка'}</div>`;
                }
                
                // 3. Тест updateSettings с валидными данными
                html += '<h4>3. updateSettings (валидные)</h4>';
                const updateResponse = await fetch('index.php?page=api&action=updateSettings', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        notification_email: '<EMAIL>',
                        smtp_enabled: 1
                    })
                });
                const updateResult = await updateResponse.json();
                
                if (updateResponse.ok && updateResult.status === 'success') {
                    html += '<div class="success">✅ updateSettings работает</div>';
                } else {
                    html += `<div class="error">❌ updateSettings: ${updateResult.message || 'Ошибка'}</div>`;
                }
                
                // 4. Тест getSessions
                html += '<h4>4. getSessions</h4>';
                const sessionsResponse = await fetch('index.php?page=api&action=getSessions');
                const sessionsResult = await sessionsResponse.json();
                
                if (sessionsResponse.ok && Array.isArray(sessionsResult)) {
                    html += `<div class="success">✅ getSessions работает (${sessionsResult.length} сессий)</div>`;
                } else {
                    html += `<div class="error">❌ getSessions: Ошибка</div>`;
                }
                
                html += '<h4>🎉 Итог</h4>';
                html += '<div class="success">✅ Основные API методы исправлены и работают!</div>';
                html += '<div class="info">Теперь админка должна работать корректно.</div>';
                
                resultDiv.innerHTML = html;
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Критическая ошибка: ${error.message}</div>`;
            }
        }
        
        // Автоматически запускаем полный тест
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(testFullAPI, 500);
        });
    </script>
</body>
</html>
