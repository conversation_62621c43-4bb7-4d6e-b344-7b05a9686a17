<!DOCTYPE html>
<html>
<head>
    <title>Тест URL streamProxy</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        #log { height: 300px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; background: #f9f9f9; }
    </style>
</head>
<body>
    <h1>🔧 Тест URL streamProxy</h1>
    
    <div>
        <h2>1. Создаем request_id</h2>
        <button onclick="createRequestId()">Создать request_id</button>
        <div id="request-result"></div>
    </div>
    
    <div>
        <h2>2. Тестируем streamProxy URL</h2>
        <button onclick="testStreamUrl()">Тест streamProxy URL</button>
        <div id="url-result"></div>
    </div>
    
    <div>
        <h2>3. Лог</h2>
        <div id="log"></div>
    </div>

    <script>
        let currentRequestId = null;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${time}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        async function createRequestId() {
            const resultDiv = document.getElementById('request-result');
            resultDiv.innerHTML = '<div class="info">Создаем request_id...</div>';
            
            try {
                const response = await fetch('index.php?page=api&action=prepareStream', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        session_id: 1,
                        user_message: 'Тестовое сообщение для URL теста'
                    })
                });
                
                const result = await response.json();
                log(`prepareStream ответ: ${JSON.stringify(result)}`);
                
                if (response.ok && result.status === 'success') {
                    currentRequestId = result.request_id;
                    resultDiv.innerHTML = `
                        <div class="success">✅ Request ID создан: ${currentRequestId}</div>
                    `;
                    log(`Request ID создан: ${currentRequestId}`);
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ Ошибка создания request_id</div>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                log(`Ошибка createRequestId: ${error.message}`);
                resultDiv.innerHTML = `<div class="error">❌ Ошибка: ${error.message}</div>`;
            }
        }
        
        async function testStreamUrl() {
            const resultDiv = document.getElementById('url-result');
            
            if (!currentRequestId) {
                resultDiv.innerHTML = '<div class="error">❌ Сначала создайте request_id</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="info">Тестируем streamProxy URL...</div>';
            
            // Формируем URL как в реальном коде
            const streamUrl = `index.php?page=api&action=streamProxy&request_id=${currentRequestId}&user_id=test_user&chat_user_id=test_user`;
            
            log(`Тестируем URL: ${streamUrl}`);
            
            // Сначала проверим URL через обычный fetch
            try {
                log('Проверяем URL через fetch...');
                const response = await fetch(streamUrl);
                log(`Fetch ответ: ${response.status} ${response.statusText}`);
                log(`Content-Type: ${response.headers.get('content-type')}`);
                
                if (response.ok) {
                    const text = await response.text();
                    log(`Первые 200 символов ответа: ${text.substring(0, 200)}`);
                    
                    if (text.includes('data:')) {
                        resultDiv.innerHTML = `
                            <div class="success">✅ URL работает! Получен SSE поток</div>
                            <div class="info">Теперь тестируем EventSource...</div>
                        `;
                        
                        // Теперь тестируем через EventSource
                        setTimeout(() => testEventSource(streamUrl), 1000);
                    } else {
                        resultDiv.innerHTML = `
                            <div class="error">❌ URL не возвращает SSE поток</div>
                            <pre>${text}</pre>
                        `;
                    }
                } else {
                    const text = await response.text();
                    resultDiv.innerHTML = `
                        <div class="error">❌ HTTP ошибка: ${response.status}</div>
                        <pre>${text}</pre>
                    `;
                }
            } catch (error) {
                log(`Ошибка fetch: ${error.message}`);
                resultDiv.innerHTML = `<div class="error">❌ Ошибка fetch: ${error.message}</div>`;
            }
        }
        
        function testEventSource(url) {
            log('Тестируем EventSource...');
            
            const eventSource = new EventSource(url);
            let messageCount = 0;
            
            eventSource.onopen = function(event) {
                log('✅ EventSource подключен');
            };
            
            eventSource.onmessage = function(event) {
                messageCount++;
                log(`📨 Сообщение #${messageCount}: ${event.data}`);
                
                try {
                    const data = JSON.parse(event.data);
                    if (data.type === 'end') {
                        log('🏁 Поток завершен');
                        eventSource.close();
                        
                        const resultDiv = document.getElementById('url-result');
                        resultDiv.innerHTML = `
                            <div class="success">✅ EventSource работает!</div>
                            <div class="info">Получено сообщений: ${messageCount}</div>
                        `;
                    }
                } catch (e) {
                    log(`⚠️ Не JSON: ${event.data}`);
                }
            };
            
            eventSource.onerror = function(event) {
                log(`❌ EventSource ошибка: ReadyState=${eventSource.readyState}`);
                
                if (eventSource.readyState === EventSource.CLOSED) {
                    log('🔌 EventSource закрыт');
                    if (messageCount === 0) {
                        const resultDiv = document.getElementById('url-result');
                        resultDiv.innerHTML = '<div class="error">❌ EventSource закрылся без данных</div>';
                    }
                }
            };
            
            // Таймаут
            setTimeout(() => {
                if (eventSource.readyState !== EventSource.CLOSED) {
                    log('⏰ Таймаут EventSource');
                    eventSource.close();
                }
            }, 15000);
        }
        
        // Автозапуск
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(createRequestId, 500);
        });
    </script>
</body>
</html>
