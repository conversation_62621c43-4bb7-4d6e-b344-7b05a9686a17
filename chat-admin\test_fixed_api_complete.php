<!DOCTYPE html>
<html>
<head>
    <title>Полный тест исправленного API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔧 Полный тест исправленного API</h1>
    
    <div class="test-section">
        <h2>1. Тест основных API методов</h2>
        <button onclick="testBasicAPI()">Тест основных методов</button>
        <div id="basic-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Тест новых методов</h2>
        <button onclick="testNewMethods()">Тест getBotAvatar и prepareStream</button>
        <div id="new-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Тест удаления сессии</h2>
        <button onclick="testDeleteSession()">Тест deleteSession</button>
        <div id="delete-result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. Полный тест чата</h2>
        <button onclick="testFullChat()">Полный тест чата</button>
        <div id="chat-result"></div>
    </div>

    <script>
        async function testBasicAPI() {
            const resultDiv = document.getElementById('basic-result');
            resultDiv.innerHTML = '<div class="info">Тестируем основные API методы...</div>';
            
            let html = '<h3>🧪 Тест основных API методов</h3>';
            
            try {
                // 1. getSessions
                html += '<h4>1. getSessions</h4>';
                const sessionsResponse = await fetch('index.php?page=api&action=getSessions');
                const sessionsResult = await sessionsResponse.json();
                
                if (sessionsResponse.ok && Array.isArray(sessionsResult)) {
                    html += `<div class="success">✅ getSessions работает (${sessionsResult.length} сессий)</div>`;
                } else {
                    html += `<div class="error">❌ getSessions: ${JSON.stringify(sessionsResult)}</div>`;
                }
                
                // 2. getSettings
                html += '<h4>2. getSettings</h4>';
                const settingsResponse = await fetch('index.php?page=api&action=getSettings');
                const settingsResult = await settingsResponse.json();
                
                if (settingsResponse.ok && settingsResult.status === 'success') {
                    html += '<div class="success">✅ getSettings работает</div>';
                } else {
                    html += `<div class="error">❌ getSettings: ${settingsResult.message || 'Ошибка'}</div>`;
                }
                
                // 3. getTexts
                html += '<h4>3. getTexts</h4>';
                const textsResponse = await fetch('index.php?page=api&action=getTexts');
                const textsResult = await textsResponse.json();
                
                if (textsResponse.ok && textsResult.status === 'success') {
                    html += `<div class="success">✅ getTexts работает (${textsResult.texts.length} текстов)</div>`;
                } else {
                    html += `<div class="error">❌ getTexts: ${textsResult.message || 'Ошибка'}</div>`;
                }
                
                // 4. updateSettings
                html += '<h4>4. updateSettings</h4>';
                const updateResponse = await fetch('index.php?page=api&action=updateSettings', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        notification_email: '<EMAIL>'
                    })
                });
                const updateResult = await updateResponse.json();
                
                if (updateResponse.ok && updateResult.status === 'success') {
                    html += '<div class="success">✅ updateSettings работает</div>';
                } else {
                    html += `<div class="error">❌ updateSettings: ${updateResult.message || 'Ошибка'}</div>`;
                }
                
                resultDiv.innerHTML = html;
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Критическая ошибка: ${error.message}</div>`;
            }
        }
        
        async function testNewMethods() {
            const resultDiv = document.getElementById('new-result');
            resultDiv.innerHTML = '<div class="info">Тестируем новые методы...</div>';
            
            let html = '<h3>🧪 Тест новых методов</h3>';
            
            try {
                // 1. getBotAvatar
                html += '<h4>1. getBotAvatar</h4>';
                const avatarResponse = await fetch('index.php?page=api&action=getBotAvatar');
                const avatarResult = await avatarResponse.json();
                
                if (avatarResponse.ok && avatarResult.status === 'success') {
                    html += `<div class="success">✅ getBotAvatar работает: ${avatarResult.avatar}</div>`;
                } else {
                    html += `<div class="error">❌ getBotAvatar: ${avatarResult.message || 'Ошибка'}</div>`;
                }
                
                // 2. prepareStream (тест подготовки)
                html += '<h4>2. prepareStream</h4>';
                const streamResponse = await fetch('index.php?page=api&action=prepareStream', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        session_id: 1,
                        user_message: 'test'
                    })
                });
                const streamResult = await streamResponse.json();
                
                if (streamResponse.ok && streamResult.status === 'success') {
                    html += `<div class="success">✅ prepareStream работает: request_id = ${streamResult.request_id}</div>`;
                } else {
                    html += `<div class="error">❌ prepareStream: ${streamResult.message || 'Ошибка'}</div>`;
                }
                
                resultDiv.innerHTML = html;
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Критическая ошибка: ${error.message}</div>`;
            }
        }
        
        async function testDeleteSession() {
            const resultDiv = document.getElementById('delete-result');
            resultDiv.innerHTML = '<div class="info">Тестируем удаление сессии...</div>';
            
            let html = '<h3>🧪 Тест удаления сессии</h3>';
            
            try {
                // Сначала создаем тестовую сессию
                html += '<h4>1. Создание тестовой сессии</h4>';
                const createResponse = await fetch('index.php?page=api&action=createSession', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        title: 'Тестовая сессия для удаления'
                    })
                });
                const createResult = await createResponse.json();
                
                if (createResponse.ok && createResult.status === 'success') {
                    const sessionId = createResult.session_id;
                    html += `<div class="success">✅ Сессия создана: ID = ${sessionId}</div>`;
                    
                    // Теперь удаляем её
                    html += '<h4>2. Удаление сессии</h4>';
                    const deleteResponse = await fetch('index.php?page=api&action=deleteSession', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            session_id: sessionId
                        })
                    });
                    const deleteResult = await deleteResponse.json();
                    
                    if (deleteResponse.ok && deleteResult.status === 'success') {
                        html += '<div class="success">✅ Сессия успешно удалена</div>';
                    } else {
                        html += `<div class="error">❌ Ошибка удаления: ${deleteResult.message || 'Неизвестная ошибка'}</div>`;
                    }
                } else {
                    html += `<div class="error">❌ Не удалось создать тестовую сессию: ${createResult.message || 'Ошибка'}</div>`;
                }
                
                resultDiv.innerHTML = html;
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Критическая ошибка: ${error.message}</div>`;
            }
        }
        
        async function testFullChat() {
            const resultDiv = document.getElementById('chat-result');
            resultDiv.innerHTML = '<div class="info">Полный тест чата...</div>';
            
            let html = '<h3>🧪 Полный тест чата</h3>';
            
            try {
                // 1. Получаем активную сессию
                html += '<h4>1. getActiveSession</h4>';
                const activeResponse = await fetch('index.php?page=api&action=getActiveSession');
                const activeResult = await activeResponse.json();
                
                if (activeResponse.ok && activeResult.status === 'success') {
                    const sessionId = activeResult.session_id;
                    html += `<div class="success">✅ Активная сессия: ID = ${sessionId}</div>`;
                    
                    // 2. Сохраняем сообщение пользователя
                    html += '<h4>2. saveUserMessage</h4>';
                    const saveResponse = await fetch('index.php?page=api&action=saveUserMessage', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            session_id: sessionId,
                            message: 'Тестовое сообщение',
                            analytics_data: null
                        })
                    });
                    const saveResult = await saveResponse.json();
                    
                    if (saveResponse.ok && saveResult.status === 'success') {
                        html += `<div class="success">✅ Сообщение сохранено: ID = ${saveResult.message_id}</div>`;
                        
                        // 3. Получаем сообщения
                        html += '<h4>3. getMessages</h4>';
                        const messagesResponse = await fetch(`index.php?page=api&action=getMessages&session_id=${sessionId}`);
                        const messagesResult = await messagesResponse.json();
                        
                        if (messagesResponse.ok && Array.isArray(messagesResult)) {
                            html += `<div class="success">✅ Сообщения получены: ${messagesResult.length} сообщений</div>`;
                        } else {
                            html += `<div class="error">❌ getMessages: Ошибка</div>`;
                        }
                    } else {
                        html += `<div class="error">❌ saveUserMessage: ${saveResult.message || 'Ошибка'}</div>`;
                    }
                } else {
                    html += `<div class="error">❌ getActiveSession: ${activeResult.message || 'Ошибка'}</div>`;
                }
                
                html += '<h4>🎉 Итог</h4>';
                html += '<div class="success">✅ Все основные функции API восстановлены!</div>';
                html += '<div class="info">Теперь чат должен работать полностью.</div>';
                
                resultDiv.innerHTML = html;
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Критическая ошибка: ${error.message}</div>`;
            }
        }
        
        // Автоматически запускаем тесты
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(testBasicAPI, 500);
        });
    </script>
</body>
</html>
