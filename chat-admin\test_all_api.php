<!DOCTYPE html>
<html>
<head>
    <title>Полный тест API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; max-height: 200px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
    </style>
</head>
<body>
    <h1>🔧 Полный тест всех API методов</h1>
    
    <div class="test-section">
        <h2>Быстрый тест всех методов</h2>
        <button onclick="testAllMethods()">Тестировать все методы</button>
        <div id="all-methods-result"></div>
    </div>
    
    <div class="test-grid">
        <div class="test-section">
            <h3>1. getSessions</h3>
            <button onclick="testMethod('getSessions')">Тест</button>
            <div id="getSessions-result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. getActiveSession</h3>
            <button onclick="testMethod('getActiveSession')">Тест</button>
            <div id="getActiveSession-result"></div>
        </div>
        
        <div class="test-section">
            <h3>3. getSettings</h3>
            <button onclick="testMethod('getSettings')">Тест</button>
            <div id="getSettings-result"></div>
        </div>
        
        <div class="test-section">
            <h3>4. getTexts</h3>
            <button onclick="testMethod('getTexts')">Тест</button>
            <div id="getTexts-result"></div>
        </div>
        
        <div class="test-section">
            <h3>5. createSession</h3>
            <button onclick="testCreateSession()">Тест</button>
            <div id="createSession-result"></div>
        </div>
        
        <div class="test-section">
            <h3>6. updateSettings</h3>
            <button onclick="testUpdateSettings()">Тест</button>
            <div id="updateSettings-result"></div>
        </div>
    </div>

    <script>
        async function testMethod(action, data = null) {
            const resultDiv = document.getElementById(action + '-result');
            resultDiv.innerHTML = '<div class="info">Тестируем...</div>';
            
            try {
                let url = `index.php?page=api&action=${action}`;
                let options = {
                    method: 'GET'
                };
                
                if (data) {
                    options.method = 'POST';
                    options.headers = {
                        'Content-Type': 'application/json',
                    };
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(url, options);
                const responseText = await response.text();
                
                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (e) {
                    throw new Error(`Не JSON ответ: ${responseText.substring(0, 200)}`);
                }
                
                if (response.ok) {
                    if (result.status === 'success' || result.length !== undefined || result.id !== undefined) {
                        resultDiv.innerHTML = `
                            <div class="success">✅ ${action} работает!</div>
                            <pre>${JSON.stringify(result, null, 2).substring(0, 500)}</pre>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="warning">⚠️ ${action} вернул неожиданный результат</div>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        `;
                    }
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ ${action} ошибка HTTP ${response.status}</div>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ ${action} ошибка: ${error.message}</div>`;
            }
        }
        
        async function testCreateSession() {
            const action = 'createSession';
            const data = {
                title: 'Тестовая сессия ' + Date.now()
            };
            
            const resultDiv = document.getElementById(action + '-result');
            resultDiv.innerHTML = '<div class="info">Создаем тестовую сессию...</div>';
            
            try {
                const response = await fetch(`index.php?page=api&action=${action}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (response.ok && result.id) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ createSession работает!</div>
                        <div class="info">Создана сессия ID: ${result.id}</div>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ createSession ошибка</div>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ createSession ошибка: ${error.message}</div>`;
            }
        }
        
        async function testUpdateSettings() {
            const action = 'updateSettings';
            const data = {
                test_setting: 'test_value_' + Date.now(),
                api_test: 'success'
            };
            
            const resultDiv = document.getElementById(action + '-result');
            resultDiv.innerHTML = '<div class="info">Обновляем настройки...</div>';
            
            try {
                const response = await fetch(`index.php?page=api&action=${action}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (response.ok && result.status === 'success') {
                    resultDiv.innerHTML = `
                        <div class="success">✅ updateSettings работает!</div>
                        <div class="info">${result.message || 'Настройки обновлены'}</div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ updateSettings ошибка</div>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ updateSettings ошибка: ${error.message}</div>`;
            }
        }
        
        async function testAllMethods() {
            const resultDiv = document.getElementById('all-methods-result');
            resultDiv.innerHTML = '<div class="info">Тестируем все методы...</div>';
            
            const methods = [
                'getSessions',
                'getActiveSession', 
                'getSettings',
                'getTexts'
            ];
            
            let results = [];
            
            for (const method of methods) {
                try {
                    const response = await fetch(`index.php?page=api&action=${method}`);
                    const data = await response.json();
                    
                    if (response.ok) {
                        results.push(`✅ ${method}: OK`);
                    } else {
                        results.push(`❌ ${method}: ${data.error || 'Ошибка'}`);
                    }
                } catch (error) {
                    results.push(`❌ ${method}: ${error.message}`);
                }
                
                // Небольшая задержка между запросами
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            // Тестируем POST методы
            try {
                const response = await fetch('index.php?page=api&action=updateSettings', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ test: 'value' })
                });
                const data = await response.json();
                
                if (response.ok && data.status === 'success') {
                    results.push('✅ updateSettings: OK');
                } else {
                    results.push(`❌ updateSettings: ${data.message || 'Ошибка'}`);
                }
            } catch (error) {
                results.push(`❌ updateSettings: ${error.message}`);
            }
            
            const successCount = results.filter(r => r.startsWith('✅')).length;
            const totalCount = results.length;
            
            let html = `<h3>Результаты тестирования (${successCount}/${totalCount})</h3>`;
            html += '<ul>';
            results.forEach(result => {
                html += `<li>${result}</li>`;
            });
            html += '</ul>';
            
            if (successCount === totalCount) {
                html += '<div class="success">🎉 Все основные API методы работают!</div>';
            } else {
                html += '<div class="error">⚠️ Есть проблемы с некоторыми методами</div>';
            }
            
            resultDiv.innerHTML = html;
        }
        
        // Автоматически запускаем тест при загрузке
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(testAllMethods, 500);
        });
    </script>
</body>
</html>
