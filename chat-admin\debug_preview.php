<!DOCTYPE html>
<html>
<head>
    <title>Отладка превью сообщений</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ccc; padding: 8px; text-align: left; }
        th { background: #f0f0f0; }
    </style>
</head>
<body>
    <h1>🔍 Отладка превью сообщений</h1>
    
    <div id="results"></div>

    <script>
        async function debugPreview() {
            const resultsDiv = document.getElementById('results');
            let html = '';
            
            try {
                // 1. Проверяем getSettings
                html += '<h2>1. Проверка getSettings</h2>';
                const settingsResponse = await fetch('index.php?page=api&action=getSettings');
                const settingsResult = await settingsResponse.json();
                
                if (settingsResponse.ok && settingsResult.status === 'success') {
                    html += '<div class="success">✅ getSettings работает</div>';
                    
                    // Ищем настройки превью
                    const settings = settingsResult.settings;
                    const previewKeys = Object.keys(settings).filter(key => 
                        key.includes('preview') || key.includes('welcome') || key.includes('message')
                    );
                    
                    if (previewKeys.length > 0) {
                        html += '<h3>Настройки превью:</h3>';
                        html += '<table>';
                        html += '<tr><th>Ключ</th><th>Значение</th></tr>';
                        previewKeys.forEach(key => {
                            html += `<tr><td>${key}</td><td>${settings[key] || 'ПУСТО'}</td></tr>`;
                        });
                        html += '</table>';
                    } else {
                        html += '<div class="error">❌ Настройки превью не найдены</div>';
                    }
                } else {
                    html += `<div class="error">❌ getSettings ошибка: ${settingsResult.message}</div>`;
                }
                
                // 2. Проверяем getTexts
                html += '<h2>2. Проверка getTexts</h2>';
                const textsResponse = await fetch('index.php?page=api&action=getTexts');
                const textsResult = await textsResponse.json();
                
                if (textsResponse.ok && textsResult.status === 'success') {
                    html += '<div class="success">✅ getTexts работает</div>';
                    
                    // Ищем тексты превью
                    const texts = textsResult.texts;
                    const previewTexts = texts.filter(text => 
                        text.key.includes('preview') || text.key.includes('welcome') || text.key.includes('greeting')
                    );
                    
                    if (previewTexts.length > 0) {
                        html += '<h3>Тексты превью:</h3>';
                        html += '<table>';
                        html += '<tr><th>Ключ</th><th>Значение</th></tr>';
                        previewTexts.forEach(text => {
                            html += `<tr><td>${text.key}</td><td>${text.value || 'ПУСТО'}</td></tr>`;
                        });
                        html += '</table>';
                    } else {
                        html += '<div class="error">❌ Тексты превью не найдены</div>';
                        
                        // Показываем все тексты для анализа
                        html += '<h3>Все доступные тексты:</h3>';
                        html += '<table>';
                        html += '<tr><th>Ключ</th><th>Значение</th></tr>';
                        texts.slice(0, 10).forEach(text => {
                            html += `<tr><td>${text.key}</td><td>${(text.value || '').substring(0, 50)}...</td></tr>`;
                        });
                        html += '</table>';
                        html += `<div class="info">Показано первых 10 из ${texts.length} текстов</div>`;
                    }
                } else {
                    html += `<div class="error">❌ getTexts ошибка: ${textsResult.message}</div>`;
                }
                
                // 3. Проверяем активную сессию
                html += '<h2>3. Проверка активной сессии</h2>';
                const sessionResponse = await fetch('index.php?page=api&action=getActiveSession');
                const sessionResult = await sessionResponse.json();
                
                if (sessionResponse.ok && sessionResult.status === 'success') {
                    html += `<div class="success">✅ Активная сессия: ID = ${sessionResult.session_id}</div>`;
                    
                    // Проверяем сообщения в сессии
                    const messagesResponse = await fetch(`index.php?page=api&action=getMessages&session_id=${sessionResult.session_id}`);
                    const messagesResult = await messagesResponse.json();
                    
                    if (messagesResponse.ok && Array.isArray(messagesResult)) {
                        html += `<div class="info">Сообщений в сессии: ${messagesResult.length}</div>`;
                        
                        if (messagesResult.length === 0) {
                            html += '<div class="info">🎯 Сессия пустая - должны показываться превью сообщения</div>';
                        } else {
                            html += '<div class="info">Сессия содержит сообщения - превью не нужны</div>';
                        }
                    } else {
                        html += '<div class="error">❌ Ошибка получения сообщений</div>';
                    }
                } else {
                    html += `<div class="error">❌ getActiveSession ошибка: ${sessionResult.message}</div>`;
                }
                
                // 4. Проверяем JavaScript файл с текстами
                html += '<h2>4. Проверка JavaScript файла с текстами</h2>';
                try {
                    const jsResponse = await fetch('chat-js/texts_ru.js');
                    if (jsResponse.ok) {
                        const jsText = await jsResponse.text();
                        html += '<div class="success">✅ texts_ru.js загружается</div>';
                        
                        if (jsText.includes('preview') || jsText.includes('welcome')) {
                            html += '<div class="success">✅ В texts_ru.js есть тексты превью</div>';
                        } else {
                            html += '<div class="error">❌ В texts_ru.js нет текстов превью</div>';
                        }
                        
                        html += `<div class="info">Размер файла: ${jsText.length} символов</div>`;
                    } else {
                        html += '<div class="error">❌ texts_ru.js не загружается</div>';
                    }
                } catch (e) {
                    html += `<div class="error">❌ Ошибка загрузки texts_ru.js: ${e.message}</div>`;
                }
                
                resultsDiv.innerHTML = html;
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Критическая ошибка: ${error.message}</div>`;
            }
        }
        
        // Автозапуск
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(debugPreview, 500);
        });
    </script>
</body>
</html>
