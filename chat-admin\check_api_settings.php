<?php
require_once __DIR__ . '/app/Models/Settings.php';

use App\Models\Settings;

echo "<h1>Проверка настроек API</h1>";

try {
    $settings = new Settings();
    $allSettings = $settings->getSettings();
    
    echo "<h2>Настройки API:</h2>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Ключ</th><th>Значение</th></tr>";
    
    $apiKeys = [
        'api_key', 'api_model', 'api_url', 
        'mistral_api_key', 'mistral_model', 'mistral_api_url',
        'custom_api_supports_stream'
    ];
    
    foreach ($apiKeys as $key) {
        $value = $allSettings[$key] ?? 'НЕ УСТАНОВЛЕНО';
        if ($key === 'api_key' || $key === 'mistral_api_key') {
            $value = $value ? substr($value, 0, 10) . '...' : 'ПУСТОЙ';
        }
        echo "<tr><td>$key</td><td>$value</td></tr>";
    }
    echo "</table>";
    
    echo "<h2>Анализ:</h2>";
    
    if (empty($allSettings['api_key']) && empty($allSettings['mistral_api_key'])) {
        echo "<div style='color: red;'>❌ НЕТ API КЛЮЧЕЙ! Нужно настроить хотя бы один API.</div>";
    } else {
        if (!empty($allSettings['api_key'])) {
            echo "<div style='color: green;'>✅ Основной API ключ настроен</div>";
            echo "<div>Модель: " . ($allSettings['api_model'] ?? 'не указана') . "</div>";
            echo "<div>URL: " . ($allSettings['api_url'] ?? 'не указан') . "</div>";
        }
        
        if (!empty($allSettings['mistral_api_key'])) {
            echo "<div style='color: green;'>✅ Mistral API ключ настроен</div>";
            echo "<div>Модель: " . ($allSettings['mistral_model'] ?? 'не указана') . "</div>";
            echo "<div>URL: " . ($allSettings['mistral_api_url'] ?? 'не указан') . "</div>";
        }
    }
    
    $streamSupport = $allSettings['custom_api_supports_stream'] ?? 0;
    echo "<div>Поддержка стриминга: " . ($streamSupport ? 'ДА' : 'НЕТ') . "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red;'>Ошибка: " . $e->getMessage() . "</div>";
}
?>
