<?php

namespace App\Controllers;

use App\Models\ChatSession;
use App\Models\Message;
use App\Models\Settings;
use App\Services\PhoneNotificationService;

/**
 * API Controller для обработки AJAX запросов от фронтенда
 */
class ApiController extends BaseController
{
    private $chatSessionModel;
    private $messageModel;
    private $settingsModel;
    private $phoneNotificationService;
    private $userId;

    public function __construct()
    {
        $this->chatSessionModel = new ChatSession();
        $this->messageModel = new Message();
        $this->settingsModel = new Settings();
        $this->phoneNotificationService = new PhoneNotificationService();
        $this->userId = $_SESSION['user_id'] ?? null;
    }

    /**
     * Главный метод обработки API запросов
     */
    public function handle($action)
    {
        header('Content-Type: application/json');

        try {
            switch ($action) {
                case 'getSessions':
                case 'getUserSessions':
                    $result = $this->getUserSessions();
                    break;
                case 'getActiveSession':
                    $result = $this->getActiveSession();
                    break;
                case 'createSession':
                    $result = $this->createSession();
                    break;
                case 'deleteSession':
                    $result = $this->deleteSession();
                    break;
                case 'updateSessionTitle':
                    $result = $this->updateSessionTitle();
                    break;
                case 'getMessages':
                    $result = $this->getMessages();
                    break;
                case 'saveUserMessage':
                    $result = $this->saveUserMessage();
                    break;
                case 'sendMessage':
                    $result = $this->sendMessage();
                    break;
                case 'saveAssistantMessage':
                    $result = $this->saveAssistantMessage();
                    break;
                case 'updateMessage':
                    $result = $this->updateMessage();
                    break;
                case 'deleteMessages':
                    $result = $this->deleteMessages();
                    break;
                case 'getSettings':
                    $this->getSettings();
                    return; // Метод сам отправляет ответ
                case 'updateSettings':
                    $this->updateSettings();
                    return; // Метод сам отправляет ответ
                case 'getTexts':
                    $this->getTexts();
                    return; // Метод сам отправляет ответ
                case 'updateText':
                    $this->updateText();
                    return; // Метод сам отправляет ответ
                case 'getBotAvatar':
                    $this->getBotAvatar();
                    return; // Метод сам отправляет ответ
                case 'prepareStream':
                    $this->prepareStream();
                    return; // Метод сам отправляет ответ
                case 'streamProxy':
                    $this->streamProxy();
                    return; // Метод сам отправляет ответ
                default:
                    throw new \Exception("Unknown action: $action", 400);
            }

            echo json_encode($result, JSON_UNESCAPED_UNICODE);
        } catch (\Exception $e) {
            http_response_code($e->getCode() ?: 500);
            echo json_encode([
                'error' => $e->getMessage(),
                'status' => 'error'
            ], JSON_UNESCAPED_UNICODE);
        }
    }

    /**
     * Получает текущий ID пользователя
     */
    private function getCurrentUserId()
    {
        return $this->userId;
    }

    /**
     * Получает список сессий пользователя
     */
    private function getUserSessions(): array
    {
        $currentUserId = $this->getCurrentUserId();
        if (!$currentUserId) {
            $currentUserId = 'anonymous_' . session_id();
        }

        $sessions = $this->chatSessionModel->getUserSessions($currentUserId);
        return ['sessions' => $sessions];
    }

    /**
     * Получает активную сессию
     */
    private function getActiveSession(): array
    {
        $currentUserId = $this->getCurrentUserId();
        if (!$currentUserId) {
            $currentUserId = 'anonymous_' . session_id();
        }

        $activeSession = $this->chatSessionModel->getActiveSession($currentUserId);
        return $activeSession ? ['session_id' => $activeSession['id']] : ['session_id' => null];
    }

    /**
     * Создает новую сессию
     */
    private function createSession(): array
    {
        $input = json_decode(file_get_contents('php://input'), true) ?: $_POST;
        $title = trim($input['title'] ?? 'Новый чат');
        $currentUserId = $this->getCurrentUserId();

        if (!$currentUserId) {
            $currentUserId = 'anonymous_' . session_id();
        }

        $sessionId = $this->chatSessionModel->createSession($currentUserId, $title);
        if (!$sessionId) {
            throw new \Exception('Failed to create session', 500);
        }

        return ['status' => 'success', 'session_id' => $sessionId];
    }

    /**
     * Удаляет сессию
     */
    private function deleteSession(): array
    {
        $input = json_decode(file_get_contents('php://input'), true) ?: $_POST;
        $sessionId = filter_var($input['session_id'] ?? null, FILTER_VALIDATE_INT);
        $currentUserId = $this->getCurrentUserId();

        if (!$sessionId) {
            throw new \Exception('Session ID is required', 400);
        }

        if (!$currentUserId) {
            $currentUserId = 'anonymous_' . session_id();
        }

        if ($this->userId && !$this->chatSessionModel->verifySessionOwnership($sessionId, $currentUserId)) {
            throw new \Exception('Session not found or access denied', 403);
        }

        $result = $this->messageModel->deleteChatSession($sessionId);
        if (!$result) {
            throw new \Exception('Failed to delete session', 500);
        }

        return ['status' => 'success'];
    }

    /**
     * Обновляет заголовок сессии
     */
    private function updateSessionTitle(): array
    {
        $input = json_decode(file_get_contents('php://input'), true) ?: $_POST;
        $sessionId = filter_var($input['session_id'] ?? null, FILTER_VALIDATE_INT);
        $title = trim($input['title'] ?? '');
        $currentUserId = $this->getCurrentUserId();

        if (!$sessionId || empty($title)) {
            throw new \Exception('Session ID and title are required', 400);
        }

        if (!$currentUserId) {
            $currentUserId = 'anonymous_' . session_id();
        }

        if ($this->userId && !$this->chatSessionModel->verifySessionOwnership($sessionId, $currentUserId)) {
            throw new \Exception('Session not found or access denied', 403);
        }

        $result = $this->chatSessionModel->updateSessionTitle($sessionId, $title);
        if (!$result) {
            throw new \Exception('Failed to update session title', 500);
        }

        return ['status' => 'success'];
    }

    /**
     * Получает сообщения сессии
     */
    private function getMessages(): array
    {
        $sessionId = filter_var($_GET['session_id'] ?? null, FILTER_VALIDATE_INT);
        $limit = filter_var($_GET['limit'] ?? 100, FILTER_VALIDATE_INT);
        $currentUserId = $this->getCurrentUserId();

        if (!$sessionId) {
            throw new \Exception('Session ID is required', 400);
        }

        if (!$currentUserId) {
            $currentUserId = 'anonymous_' . session_id();
        }

        if ($this->userId && !$this->chatSessionModel->verifySessionOwnership($sessionId, $currentUserId)) {
            throw new \Exception('Session not found or access denied', 403);
        }

        $messages = $this->messageModel->getSessionMessages($sessionId, $limit);
        return ['messages' => $messages];
    }

    /**
     * Сохраняет сообщение пользователя
     */
    private function saveUserMessage(): array
    {
        $input = json_decode(file_get_contents('php://input'), true) ?: $_POST;
        $sessionId = filter_var($input['session_id'] ?? null, FILTER_VALIDATE_INT);
        $messageContent = trim(strip_tags($input['message'] ?? ''));
        $analyticsData = $input['analytics_data'] ?? null;
        $currentUserId = $this->getCurrentUserId();

        if (empty($messageContent)) throw new \Exception('Message content cannot be empty', 400);
        if (empty($sessionId)) throw new \Exception('Session ID is required', 400);

        if (!$currentUserId) {
            $currentUserId = 'anonymous_' . session_id();
        }

        if ($this->userId && !$this->chatSessionModel->verifySessionOwnership($sessionId, $currentUserId)) {
             throw new \Exception('Session not found or access denied', 403);
        }

        $messageId = $this->messageModel->addMessage($sessionId, 'user', $messageContent, $currentUserId);
        if (!$messageId) {
            throw new \Exception('Failed to save user message', 500);
        }

        // Проверяем сообщение на наличие телефона и отправляем уведомление
        try {
            error_log("ApiController::saveUserMessage - Analytics data being passed: " . (is_string($analyticsData) ? $analyticsData : json_encode($analyticsData)));
            $this->phoneNotificationService->checkAndNotify($sessionId, $messageContent, $currentUserId, $analyticsData);
        } catch (\Exception $e) {
            // Логируем ошибку, но не прерываем основной процесс
            error_log("Phone notification failed after saving message: " . $e->getMessage());
        }

        return ['status' => 'success', 'user_message_id' => $messageId];
    }

    /**
     * Отправляет сообщение (альтернативный метод)
     */
    private function sendMessage(): array
    {
        $input = json_decode(file_get_contents('php://input'), true) ?: $_POST;
        $sessionId = filter_var($input['session_id'] ?? null, FILTER_VALIDATE_INT);
        $messageContent = trim(strip_tags($input['message'] ?? ''));
        $analyticsData = $input['analytics_data'] ?? null;
        $currentUserId = $this->getCurrentUserId();

        if (empty($messageContent)) throw new \Exception('Message content cannot be empty', 400);
        if (empty($sessionId)) throw new \Exception('Session ID is required', 400);

        if (!$currentUserId) {
            $currentUserId = 'anonymous_' . session_id();
        }

        if ($this->userId && !$this->chatSessionModel->verifySessionOwnership($sessionId, $currentUserId)) {
             throw new \Exception('Session not found or access denied', 403);
        }

        $messageId = $this->messageModel->addMessage($sessionId, 'user', $messageContent, $currentUserId);
        if (!$messageId) {
            throw new \Exception('Failed to save user message', 500);
        }

        // Проверяем сообщение на наличие телефона и отправляем уведомление
        try {
            error_log("ApiController::sendMessage - Analytics data being passed: " . (is_string($analyticsData) ? $analyticsData : json_encode($analyticsData)));
            $this->phoneNotificationService->checkAndNotify($sessionId, $messageContent, $currentUserId, $analyticsData);
        } catch (\Exception $e) {
            // Логируем ошибку, но не прерываем основной процесс
            error_log("Phone notification failed after sending message: " . $e->getMessage());
        }

        return ['status' => 'success', 'user_message_id' => $messageId];
    }

    /**
     * Сохраняет сообщение ассистента
     */
    private function saveAssistantMessage(): array
    {
        $input = json_decode(file_get_contents('php://input'), true) ?: $_POST;
        $sessionId = filter_var($input['session_id'] ?? null, FILTER_VALIDATE_INT);
        $messageContent = trim($input['message'] ?? '');
        $tempId = $input['temp_id'] ?? null;
        $currentUserId = $this->getCurrentUserId();

        if (empty($messageContent)) throw new \Exception('Message content cannot be empty', 400);
        if (empty($sessionId)) throw new \Exception('Session ID is required', 400);

        if (!$currentUserId) {
            $currentUserId = 'anonymous_' . session_id();
        }

        if ($this->userId && !$this->chatSessionModel->verifySessionOwnership($sessionId, $currentUserId)) {
             throw new \Exception('Session not found or access denied', 403);
        }

        $messageId = $this->messageModel->addMessage($sessionId, 'assistant', $messageContent, $currentUserId);
        if (!$messageId) {
            throw new \Exception('Failed to save assistant message', 500);
        }

        return ['status' => 'success', 'assistant_message_id' => $messageId];
    }

    /**
     * Обновляет сообщение
     */
    private function updateMessage(): array
    {
        $input = json_decode(file_get_contents('php://input'), true) ?: $_POST;
        $sessionId = filter_var($input['session_id'] ?? null, FILTER_VALIDATE_INT);
        $messageId = filter_var($input['message_id'] ?? null, FILTER_VALIDATE_INT);
        $content = trim($input['content'] ?? '');
        $currentUserId = $this->getCurrentUserId();

        if (empty($content)) throw new \Exception('Message content cannot be empty', 400);
        if (empty($sessionId)) throw new \Exception('Session ID is required', 400);
        if (empty($messageId)) throw new \Exception('Message ID is required', 400);

        if (!$currentUserId) {
            $currentUserId = 'anonymous_' . session_id();
        }

        if ($this->userId && !$this->chatSessionModel->verifySessionOwnership($sessionId, $currentUserId)) {
             throw new \Exception('Session not found or access denied', 403);
        }

        $result = $this->messageModel->updateMessage($messageId, $content);
        if (!$result) {
            throw new \Exception('Failed to update message', 500);
        }

        return ['status' => 'success'];
    }

    /**
     * Удаляет сообщения
     */
    private function deleteMessages(): array
    {
        $input = json_decode(file_get_contents('php://input'), true) ?: $_POST;
        $sessionId = filter_var($input['session_id'] ?? null, FILTER_VALIDATE_INT);
        $messageIds = $input['message_ids'] ?? [];
        $currentUserId = $this->getCurrentUserId();

        if (empty($sessionId)) throw new \Exception('Session ID is required', 400);
        if (empty($messageIds) || !is_array($messageIds)) throw new \Exception('Message IDs are required', 400);

        if (!$currentUserId) {
            $currentUserId = 'anonymous_' . session_id();
        }

        if ($this->userId && !$this->chatSessionModel->verifySessionOwnership($sessionId, $currentUserId)) {
             throw new \Exception('Session not found or access denied', 403);
        }

        foreach ($messageIds as $messageId) {
            $this->messageModel->deleteMessage($messageId);
        }

        return ['status' => 'success'];
    }

    /**
     * Получает настройки
     */
    private function getSettings(): void
    {
        try {
            $settings = $this->settingsModel->getSettings();
            $this->sendJsonResponse([
                'status' => 'success',
                'settings' => $settings
            ]);
        } catch (\Exception $e) {
            error_log("ApiController: Error in getSettings: " . $e->getMessage());
            $this->sendJsonResponse([
                'status' => 'error',
                'message' => 'Ошибка загрузки настроек: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Обновляет настройки
     */
    private function updateSettings(): void
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true) ?: $_POST;

            if (empty($input)) {
                $this->sendJsonResponse([
                    'status' => 'error',
                    'message' => 'Не переданы данные для обновления'
                ]);
                return;
            }

            $success = true;
            $errors = [];

            foreach ($input as $key => $value) {
                if (!$this->settingsModel->updateSetting($key, $value)) {
                    $success = false;
                    $errors[] = "Ошибка обновления настройки: $key";
                }
            }

            if ($success) {
                $this->sendJsonResponse([
                    'status' => 'success',
                    'message' => 'Настройки успешно обновлены'
                ]);
            } else {
                $this->sendJsonResponse([
                    'status' => 'error',
                    'message' => 'Ошибки при обновлении настроек: ' . implode(', ', $errors)
                ]);
            }

        } catch (\Exception $e) {
            error_log("ApiController: Error in updateSettings: " . $e->getMessage());
            $this->sendJsonResponse([
                'status' => 'error',
                'message' => 'Ошибка обновления настроек: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Получение всех текстов для админки
     */
    private function getTexts(): void
    {
        try {
            $dbPath = dirname(__DIR__, 2) . '/db/chat.db';
            $pdo = new \PDO("sqlite:$dbPath");

            $stmt = $pdo->prepare("SELECT id, key, value FROM texts ORDER BY key");
            $stmt->execute();
            $texts = $stmt->fetchAll(\PDO::FETCH_ASSOC);

            $this->sendJsonResponse([
                'status' => 'success',
                'texts' => $texts
            ]);

        } catch (\Exception $e) {
            error_log("ApiController: Error in getTexts: " . $e->getMessage());
            $this->sendJsonResponse([
                'status' => 'error',
                'message' => 'Ошибка загрузки текстов: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Обновление текста
     */
    private function updateText(): void
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);

            if (!isset($input['id']) || !isset($input['content'])) {
                $this->sendJsonResponse([
                    'status' => 'error',
                    'message' => 'Не указаны обязательные параметры'
                ]);
                return;
            }

            $id = $input['id'];
            $content = trim($input['content']);

            if (empty($content)) {
                $this->sendJsonResponse([
                    'status' => 'error',
                    'message' => 'Текст не может быть пустым'
                ]);
                return;
            }

            $dbPath = dirname(__DIR__, 2) . '/db/chat.db';
            $pdo = new \PDO("sqlite:$dbPath");

            $stmt = $pdo->prepare("UPDATE texts SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
            $result = $stmt->execute([$content, $id]);

            if ($result) {
                // Регенерируем JS файл
                $this->regenerateTextsJsFile($pdo);

                $this->sendJsonResponse([
                    'status' => 'success',
                    'message' => 'Текст успешно обновлен'
                ]);
            } else {
                $this->sendJsonResponse([
                    'status' => 'error',
                    'message' => 'Ошибка обновления текста'
                ]);
            }

        } catch (\Exception $e) {
            error_log("ApiController: Error in updateText: " . $e->getMessage());
            $this->sendJsonResponse([
                'status' => 'error',
                'message' => 'Ошибка обновления текста: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Регенерация JS файла с текстами
     */
    private function regenerateTextsJsFile(\PDO $pdo): void
    {
        try {
            $stmt = $pdo->prepare("SELECT key, value FROM texts ORDER BY key");
            $stmt->execute();
            $texts = $stmt->fetchAll(\PDO::FETCH_ASSOC);

            $jsContent = "// Автоматически сгенерированный файл с текстами\n";
            $jsContent .= "// Дата генерации: " . date('Y-m-d H:i:s') . "\n";
            $jsContent .= "// Количество текстов: " . count($texts) . "\n\n";
            $jsContent .= "const texts = {};\n\n";

            foreach ($texts as $text) {
                $jsContent .= "texts['" . addslashes($text['key']) . "'] = '" . addslashes($text['value']) . "';\n";
            }

            $jsContent .= "\n// Экспорт для использования в модулях\n";
            $jsContent .= "if (typeof module !== 'undefined' && module.exports) {\n";
            $jsContent .= "    module.exports = texts;\n";
            $jsContent .= "} else if (typeof window !== 'undefined') {\n";
            $jsContent .= "    window.texts = texts;\n";
            $jsContent .= "}\n";

            $jsFilePath = dirname(__DIR__, 2) . '/chat-js/texts_ru.js';
            file_put_contents($jsFilePath, $jsContent);

            error_log("ApiController: JS файл с текстами регенерирован");

        } catch (\Exception $e) {
            error_log("ApiController: Error regenerating JS file: " . $e->getMessage());
        }
    }

    /**
     * Возвращает путь к аватару бота
     */
    private function getBotAvatar(): void
    {
        try {
            $settings = $this->settingsModel->getSettings();
            $avatar = $settings['bot_avatar'] ?? '/chat-admin/images/darya.svg';

            $this->sendJsonResponse([
                'status' => 'success',
                'avatar' => $avatar
            ]);
        } catch (\Exception $e) {
            error_log("ApiController: Error in getBotAvatar: " . $e->getMessage());
            $this->sendJsonResponse([
                'status' => 'error',
                'message' => 'Ошибка получения аватара: ' . $e->getMessage(),
                'avatar' => '/chat-admin/images/darya.svg' // fallback
            ]);
        }
    }

    /**
     * Подготавливает поток для стриминга
     */
    private function prepareStream(): void
    {
        try {
            // Делегируем выполнение StreamController
            $streamController = new \App\Controllers\StreamController();
            $streamController->prepareStream();
        } catch (\Exception $e) {
            error_log("ApiController: Error in prepareStream: " . $e->getMessage());
            http_response_code(500);
            echo json_encode([
                'error' => 'Server error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Обрабатывает потоковые запросы
     */
    private function streamProxy(): void
    {
        try {
            // Делегируем выполнение StreamController
            $streamController = new \App\Controllers\StreamController();
            $streamController->streamProxy();
        } catch (\Exception $e) {
            error_log("ApiController: Error in streamProxy: " . $e->getMessage());
            header('Content-Type: text/event-stream');
            echo "data: " . json_encode(['type' => 'error', 'message' => $e->getMessage()]) . "\n\n";
            flush();
        }
    }
}
