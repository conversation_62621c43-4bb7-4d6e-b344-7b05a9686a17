<?php
// Тест прямого обращения к streamProxy
echo "<h1>Тест streamProxy</h1>";

// Сначала создаем тестовый request_id
$requestId = 'stream_' . uniqid('test_', true);
echo "<p>Создаем тестовый request_id: $requestId</p>";

// Создаем тестовый файл
$tmpDir = __DIR__ . '/tmp';
if (!is_dir($tmpDir)) {
    mkdir($tmpDir, 0755, true);
}

$testData = [
    'session_id' => 1,
    'user_message' => 'Тестовое сообщение'
];

$filePath = $tmpDir . '/' . $requestId . '.json';
file_put_contents($filePath, json_encode($testData));
echo "<p>Создан файл: $filePath</p>";

// Теперь тестируем streamProxy
echo "<h2>Тест streamProxy напрямую:</h2>";

try {
    // Имитируем GET параметры
    $_GET['request_id'] = $requestId;
    $_GET['user_id'] = 'test_user';
    $_GET['chat_user_id'] = 'test_user';
    
    echo "<p>Параметры: request_id=$requestId</p>";
    
    // Подключаем автозагрузчик
    require_once __DIR__ . '/app/autoload.php';
    
    $apiController = new \App\Controllers\ApiController();
    
    // Используем рефлексию для вызова приватного метода
    $reflection = new ReflectionClass($apiController);
    $method = $reflection->getMethod('streamProxy');
    $method->setAccessible(true);
    
    echo "<p>Вызываем streamProxy...</p>";
    
    // Захватываем вывод
    ob_start();
    $method->invoke($apiController);
    $output = ob_get_contents();
    ob_end_clean();
    
    echo "<h3>Результат streamProxy:</h3>";
    echo "<pre>" . htmlspecialchars($output) . "</pre>";
    
    if (empty($output)) {
        echo "<p style='color: red;'>❌ Пустой вывод от streamProxy</p>";
    } else {
        echo "<p style='color: green;'>✅ streamProxy вернул данные</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Ошибка: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

// Очищаем тестовый файл
if (file_exists($filePath)) {
    unlink($filePath);
    echo "<p>Тестовый файл удален</p>";
}
?>
