<?php

namespace App\Models;

use App\Models\Database;

class Settings {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * Получает текущие настройки API.
     * Предполагается, что в таблице только одна строка настроек.
     *
     * @return array|false Ассоциативный массив с настройками или false, если настроек нет или произошла ошибка.
     */
    public function getSettings(): array|false {
        try {
            $stmt = $this->db->prepare("SELECT * FROM api_settings LIMIT 1");
            $result = $stmt->execute();
            // fetchArray(SQLITE3_ASSOC) возвращает false, если нет строк
            return $result->fetchArray(\SQLITE3_ASSOC);
        } catch (\Exception $e) {
            error_log("Error fetching API settings: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Сохраняет настройки API. Обновляет существующую запись или создает новую.
     *
     * @param array $data Ассоциативный массив с данными настроек:
     *                    'api_key' (string) - Обязательно
     *                    'api_model' (string) - Обязательно
     *                    'system_message' (string) - Опционально
     *                    'is_active' (int) - 0 или 1, Обязательно
     *                    'mistral_api_url' (string) - Опционально
     *                    'mistral_max_tokens' (int) - Опционально
     *                    'tts_voice' (string) - Опционально
     *                    'tts_locale' (string) - Опционально
     *                    'bot_avatar' (string) - Опционально
     *                    'custom_api_supports_stream' (int) - Опционально
     *                    'chat_primary_color' (string) - Опционально
     *                    'chat_primary_dark_color' (string) - Опционально
     *                    'chat_secondary_color' (string) - Опционально
     *                    'chat_text_color' (string) - Опционально
     *                    'chat_text_light_color' (string) - Опционально
     *                    'chat_bg_color' (string) - Опционально
     *                    'chat_textarea_edited_color' (string) - Опционально
     *                    'chat_card_color' (string) - Опционально
     *                    'chat_error_color' (string) - Опционально
     *                    'chat_error_gentle_color' (string) - Опционально
     *                    'chat_error_gentle_dark_color' (string) - Опционально
     *                    'chat_success_color' (string) - Опционально
     *                    'chat_border_color' (string) - Опционально
     *                    'chat_shadow_color' (string) - Опционально
     *                    'chat_shadow_soft_color' (string) - Опционально
     *                    'chat_error_gentle_gradient_start' (string) - Опционально
     *                    'chat_error_gentle_gradient_end' (string) - Опционально
     * @return bool True в случае успеха, false в случае ошибки.
     */
    public function saveSettings(array $data): bool {
        // Простая валидация наличия обязательных полей
        if (empty($data['api_key'])) {
             error_log("Save settings failed: Missing required field 'api_key'.");
             return false;
        }

        $currentSettings = $this->getSettings();

        try {
            if ($currentSettings) {
                // Обновляем существующие настройки
                $stmt = $this->db->prepare("UPDATE api_settings SET
                                            api_key = :api_key,
                                            api_model = :api_model,
                                            system_message = :system_message,
                                            is_active = :is_active,
                                            mistral_api_url = :mistral_api_url,
                                            mistral_max_tokens = :mistral_max_tokens,
                                            tts_voice = :tts_voice,
                                            tts_locale = :tts_locale,
                                            bot_avatar = :bot_avatar,
                                            custom_api_supports_stream = :custom_api_supports_stream,
                                            chat_primary_color = :chat_primary_color,
                                            chat_primary_dark_color = :chat_primary_dark_color,
                                            chat_secondary_color = :chat_secondary_color,
                                            chat_text_color = :chat_text_color,
                                            chat_text_light_color = :chat_text_light_color,
                                            chat_bg_color = :chat_bg_color,
                                            chat_textarea_edited_color = :chat_textarea_edited_color,
                                            chat_card_color = :chat_card_color,
                                            chat_error_color = :chat_error_color,
                                            chat_error_gentle_color = :chat_error_gentle_color,
                                            chat_error_gentle_dark_color = :chat_error_gentle_dark_color,
                                            chat_success_color = :chat_success_color,
                                            chat_border_color = :chat_border_color,
                                            chat_shadow_color = :chat_shadow_color,
                                            chat_shadow_soft_color = :chat_shadow_soft_color,
                                            chat_error_gentle_gradient_start = :chat_error_gentle_gradient_start,
                                            chat_error_gentle_gradient_end = :chat_error_gentle_gradient_end,
                                            updated_at = CURRENT_TIMESTAMP");
            } else {
                // Создаем новые настройки (если таблицы пуста)
                $stmt = $this->db->prepare("INSERT INTO api_settings
                                            (api_key, api_model, system_message, is_active, mistral_api_url, mistral_max_tokens, tts_voice, tts_locale, bot_avatar, custom_api_supports_stream, chat_primary_color, chat_primary_dark_color, chat_secondary_color, chat_text_color, chat_text_light_color, chat_bg_color, chat_textarea_edited_color, chat_card_color, chat_error_color, chat_error_gentle_color, chat_error_gentle_dark_color, chat_success_color, chat_border_color, chat_shadow_color, chat_shadow_soft_color, chat_error_gentle_gradient_start, chat_error_gentle_gradient_end)
                                            VALUES (:api_key, :api_model, :system_message, :is_active, :mistral_api_url, :mistral_max_tokens, :tts_voice, :tts_locale, :bot_avatar, :custom_api_supports_stream, :chat_primary_color, :chat_primary_dark_color, :chat_secondary_color, :chat_text_color, :chat_text_light_color, :chat_bg_color, :chat_textarea_edited_color, :chat_card_color, :chat_error_color, :chat_error_gentle_color, :chat_error_gentle_dark_color, :chat_success_color, :chat_border_color, :chat_shadow_color, :chat_shadow_soft_color, :chat_error_gentle_gradient_start, :chat_error_gentle_gradient_end)");
            // ИСПРАВЛЕНИЕ: Удалена лишняя закрывающая фигурная скобка '}' после блока else
            }

            // Привязываем значения
            $stmt->bindValue(':api_key', trim($data['api_key']), \SQLITE3_TEXT);
            $stmt->bindValue(':api_model', trim($data['api_model'] ?? 'mistral-large-latest'), \SQLITE3_TEXT);
            $stmt->bindValue(':system_message', trim($data['system_message'] ?? ''), \SQLITE3_TEXT);
            $stmt->bindValue(':is_active', isset($data['is_active']) ? (int)$data['is_active'] : 1, \SQLITE3_INTEGER);
            $stmt->bindValue(':mistral_api_url', isset($data['mistral_api_url']) ? trim($data['mistral_api_url']) : null, $data['mistral_api_url'] === null ? \SQLITE3_NULL : \SQLITE3_TEXT); // Учитываем NULL
            $stmt->bindValue(':mistral_max_tokens', isset($data['mistral_max_tokens']) ? (int)$data['mistral_max_tokens'] : 2048, \SQLITE3_INTEGER);
            $stmt->bindValue(':tts_voice', isset($data['tts_voice']) ? trim($data['tts_voice']) : 'SvetlanaNeural', \SQLITE3_TEXT);
            $stmt->bindValue(':tts_locale', isset($data['tts_locale']) ? trim($data['tts_locale']) : 'ru-RU', \SQLITE3_TEXT);
            $stmt->bindValue(':bot_avatar', isset($data['bot_avatar']) ? trim($data['bot_avatar']) : '/chat-admin/images/darya.svg', \SQLITE3_TEXT);
            $stmt->bindValue(':custom_api_supports_stream', isset($data['custom_api_supports_stream']) ? (int)$data['custom_api_supports_stream'] : 0, \SQLITE3_INTEGER);

            // Привязываем значения для новых полей цветов
            $stmt->bindValue(':chat_primary_color', trim($data['chat_primary_color'] ?? '#4361ee'), \SQLITE3_TEXT);
            $stmt->bindValue(':chat_primary_dark_color', trim($data['chat_primary_dark_color'] ?? '#3a56d4'), \SQLITE3_TEXT);
            $stmt->bindValue(':chat_secondary_color', trim($data['chat_secondary_color'] ?? '#3f37c9'), \SQLITE3_TEXT);
            $stmt->bindValue(':chat_text_color', trim($data['chat_text_color'] ?? '#79818f'), \SQLITE3_TEXT);
            $stmt->bindValue(':chat_text_light_color', trim($data['chat_text_light_color'] ?? '#8d99ae'), \SQLITE3_TEXT);
            $stmt->bindValue(':chat_bg_color', trim($data['chat_bg_color'] ?? '#f8f9fa'), \SQLITE3_TEXT);
            $stmt->bindValue(':chat_textarea_edited_color', trim($data['chat_textarea_edited_color'] ?? '#f8f9fa'), \SQLITE3_TEXT);
            $stmt->bindValue(':chat_card_color', trim($data['chat_card_color'] ?? '#ffffff'), \SQLITE3_TEXT);
            $stmt->bindValue(':chat_error_color', trim($data['chat_error_color'] ?? '#ef233c'), \SQLITE3_TEXT);
            $stmt->bindValue(':chat_error_gentle_color', trim($data['chat_error_gentle_color'] ?? '#ff7f50'), \SQLITE3_TEXT);
            $stmt->bindValue(':chat_error_gentle_dark_color', trim($data['chat_error_gentle_dark_color'] ?? '#e57373'), \SQLITE3_TEXT);
            $stmt->bindValue(':chat_success_color', trim($data['chat_success_color'] ?? '#4cc9f0'), \SQLITE3_TEXT);
            $stmt->bindValue(':chat_border_color', trim($data['chat_border_color'] ?? 'rgba(0, 0, 0, 0.1)'), \SQLITE3_TEXT);
            $stmt->bindValue(':chat_shadow_color', trim($data['chat_shadow_color'] ?? '0 4px 20px rgba(0, 0, 0, 0.08)'), \SQLITE3_TEXT);
            $stmt->bindValue(':chat_shadow_soft_color', trim($data['chat_shadow_soft_color'] ?? '0 8px 30px rgba(0, 0, 0, 0.1)'), \SQLITE3_TEXT);
            $stmt->bindValue(':chat_error_gentle_gradient_start', trim($data['chat_error_gentle_gradient_start'] ?? '#f08080'), \SQLITE3_TEXT);
            $stmt->bindValue(':chat_error_gentle_gradient_end', trim($data['chat_error_gentle_gradient_end'] ?? '#e57373'), \SQLITE3_TEXT);

            // Выполняем запрос
            $result = $stmt->execute();

            // Проверяем результат выполнения
            if (!$result) {
                // Логируем ошибку SQLite, если execute вернул false
                error_log("Error executing settings save statement: " . $this->db->lastErrorMsg());
                return false;
            }

            // Если выполнение успешно, возвращаем true
            return true;

        } catch (\Exception $e) {
            error_log("Error saving API settings: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Обновляет несколько настроек одновременно
     *
     * @param array $settings Ассоциативный массив настроек для обновления
     * @return bool True в случае успеха, false в случае ошибки
     */
    public function updateSettings(array $settings): bool {
        try {
            $success = true;
            foreach ($settings as $key => $value) {
                if (!$this->updateSetting($key, $value)) {
                    $success = false;
                    error_log("Failed to update setting: $key");
                }
            }
            return $success;
        } catch (\Exception $e) {
            error_log("Error updating settings: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Обновляет одну настройку
     *
     * @param string $key Ключ настройки
     * @param mixed $value Значение настройки
     * @return bool True в случае успеха, false в случае ошибки
     */
    public function updateSetting(string $key, $value): bool {
        try {
            // Проверяем, существует ли запись настроек
            $currentSettings = $this->getSettings();

            if (!$currentSettings) {
                // Если настроек нет, создаем базовую запись
                $defaultData = [
                    'api_key' => '',
                    'api_model' => 'mistral-large-latest',
                    'is_active' => 1
                ];
                $defaultData[$key] = $value;
                return $this->saveSettings($defaultData);
            }

            // Получаем список всех колонок таблицы
            $columnsResult = $this->db->query("PRAGMA table_info(api_settings)");
            $validColumns = [];
            while ($row = $columnsResult->fetchArray(\SQLITE3_ASSOC)) {
                $validColumns[] = $row['name'];
            }

            // Проверяем, существует ли колонка
            if (!in_array($key, $validColumns)) {
                error_log("Error updating setting $key: Column does not exist in api_settings table");
                return false;
            }

            // Обновляем конкретную настройку
            $stmt = $this->db->prepare("UPDATE api_settings SET `$key` = :value, updated_at = CURRENT_TIMESTAMP");
            $stmt->bindValue(':value', $value, is_int($value) ? \SQLITE3_INTEGER : \SQLITE3_TEXT);

            $result = $stmt->execute();

            if (!$result) {
                error_log("Error updating setting $key: " . $this->db->lastErrorMsg());
                return false;
            }

            return true;

        } catch (\Exception $e) {
            error_log("Error updating setting $key: " . $e->getMessage());
            return false;
        }
    }
}